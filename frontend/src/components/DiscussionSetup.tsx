import { useState } from 'react';
import { useApp } from '../context/AppContext';
import { DiscussionConfig } from '../types';
import { MessageCircle, Users, Settings, Play, AlertCircle } from 'lucide-react';
export default function DiscussionSetup() {
  const { state, startDiscussion } = useApp();
  const [config, setConfig] = useState<DiscussionConfig>({
    topic: '',
    mode: 'free',
    selectedAgents: [],
  });
  const [errors, setErrors] = useState<string[]>([]);
  const validateConfig = (): boolean => {
    const newErrors: string[] = [];
    if (!config.topic.trim()) {
      newErrors.push('请输入讨论话题');
    }
    if (config.selectedAgents.length < 2) {
      newErrors.push('至少需要选择2个智能体参与讨论');
    }
    if (config.selectedAgents.length > 8) {
      newErrors.push('最多支持8个智能体同时讨论');
    }
    setErrors(newErrors);
    return newErrors.length === 0;
  };
  const handleStartDiscussion = () => {
    if (validateConfig()) {
      startDiscussion(config);
    }
  };
  const toggleAgent = (agentId: string) => {
    setConfig(prev => ({
      ...prev,
      selectedAgents: prev.selectedAgents.includes(agentId)
        ? prev.selectedAgents.filter(id => id !== agentId)
        : [...prev.selectedAgents, agentId]
    }));
  };
  const activeAgents = state.agents.filter(agent => agent.isActive);
  if (activeAgents.length === 0) {
    return (
      <div className="h-full bg-gradient-to-br from-orange-50 to-red-50 overflow-y-auto">
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-white rounded-xl shadow-lg p-8 text-center">
            <AlertCircle size={64} className="text-orange-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">没有可用的智能体</h2>
            <p className="text-gray-600 mb-6">
              您需要先创建和配置智能体才能开始讨论。
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              前往智能体管理
            </button>
          </div>
        </div>
      </div>
    );
  }
  return (
    <div className="h-full bg-gradient-to-br from-purple-50 to-pink-50 w-full overflow-y-auto">
      <div className="flex justify-center p-6">
        <div className="bg-white rounded-xl shadow-lg overflow-hidden" style={{width: '800px', maxWidth: '800px'}}>
          {/* 头部 */}
          <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-8">
            <div className="flex items-center gap-3 mb-4">
              <MessageCircle size={32} />
              <h1 className="text-3xl font-bold">创建新讨论</h1>
            </div>
            <p className="text-purple-100">
              配置讨论话题、模式和参与者，开始智能体之间的协作讨论
            </p>
          </div>
          <div className="p-8 space-y-8">
            {/* 错误提示 */}
            {errors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle size={20} className="text-red-600" />
                  <h3 className="font-medium text-red-800">配置错误</h3>
                </div>
                <ul className="text-red-700 text-sm space-y-1">
                  {errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}
            {/* 讨论话题 */}
            <div className="space-y-3">
              <label className="block text-lg font-semibold text-gray-900">
                讨论话题
              </label>
              <textarea
                value={config.topic}
                onChange={(e) => setConfig(prev => ({ ...prev, topic: e.target.value }))}
                placeholder="请输入您想要讨论的话题，例如：如何提升用户体验设计质量？"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                rows={3}
              />
              <p className="text-sm text-gray-500">
                清晰的话题描述有助于智能体更好地理解和参与讨论
              </p>
            </div>
            {/* 讨论模式 */}
            <div className="space-y-4">
              <label className="block text-lg font-semibold text-gray-900">
                讨论模式
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={() => setConfig(prev => ({ ...prev, mode: 'free' }))}
                  className={`p-6 rounded-xl border-2 transition-all text-left ${
                    config.mode === 'free'
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center gap-3 mb-3">
                    <MessageCircle size={24} className={config.mode === 'free' ? 'text-purple-600' : 'text-gray-600'} />
                    <h3 className="font-semibold text-gray-900">自由讨论模式</h3>
                  </div>
                  <p className="text-gray-600 text-sm">
                    智能体根据话题相关性和兴趣自主发言，讨论更加自然流畅
                  </p>
                </button>
                <button
                  onClick={() => setConfig(prev => ({ ...prev, mode: 'moderator' }))}
                  className={`p-6 rounded-xl border-2 transition-all text-left ${
                    config.mode === 'moderator'
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center gap-3 mb-3">
                    <Settings size={24} className={config.mode === 'moderator' ? 'text-purple-600' : 'text-gray-600'} />
                    <h3 className="font-semibold text-gray-900">主持人模式</h3>
                  </div>
                  <p className="text-gray-600 text-sm">
                    选择一个智能体作为主持人，按轮次组织讨论，更加有序规范
                  </p>
                </button>
              </div>
            </div>
            {/* 参与者选择 */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Users size={24} className="text-purple-600" />
                <h2 className="text-lg font-semibold text-gray-900">
                  选择参与者 ({config.selectedAgents.length}/8)
                </h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {activeAgents.map((agent) => (
                  <button
                    key={agent.id}
                    onClick={() => toggleAgent(agent.id)}
                    className={`p-4 rounded-xl border-2 transition-all text-left ${
                      config.selectedAgents.includes(agent.id)
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <img
                        src={agent.avatar}
                        alt={agent.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                      <div>
                        <h3 className="font-medium text-gray-900">{agent.name}</h3>
                        <p className="text-sm text-gray-500">
                          {agent.expertise.slice(0, 2).join('、')}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                        {agent.thinkingStyle === 'logical' && '逻辑型'}
                        {agent.thinkingStyle === 'creative' && '创意型'}
                        {agent.thinkingStyle === 'analytical' && '分析型'}
                        {agent.thinkingStyle === 'intuitive' && '直觉型'}
                        {agent.thinkingStyle === 'systematic' && '系统型'}
                      </span>
                      
                      {config.selectedAgents.includes(agent.id) && (
                        <div className="w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                      )}
                    </div>
                  </button>
                ))}
              </div>
              <p className="text-sm text-gray-500">
                建议选择具有不同专业背景和思维方式的智能体，以获得更丰富的讨论视角
              </p>
            </div>
            {/* 高级设置 */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">高级设置（可选）</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    最大消息数量
                  </label>
                  <input
                    type="number"
                    min="10"
                    max="100"
                    value={config.maxMessages || ''}
                    onChange={(e) => setConfig(prev => ({ 
                      ...prev, 
                      maxMessages: e.target.value ? parseInt(e.target.value) : undefined 
                    }))}
                    placeholder="不限制"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    时间限制（分钟）
                  </label>
                  <input
                    type="number"
                    min="5"
                    max="120"
                    value={config.timeLimit || ''}
                    onChange={(e) => setConfig(prev => ({ 
                      ...prev, 
                      timeLimit: e.target.value ? parseInt(e.target.value) : undefined 
                    }))}
                    placeholder="不限制"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
            {/* 开始按钮 */}
            <div className="flex justify-center pt-6">
              <button
                onClick={handleStartDiscussion}
                disabled={!config.topic.trim() || config.selectedAgents.length < 2}
                className="flex items-center gap-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl hover:from-purple-700 hover:to-pink-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all shadow-lg text-lg font-medium"
              >
                <Play size={24} />
                开始讨论
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
