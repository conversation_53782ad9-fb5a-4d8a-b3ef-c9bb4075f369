import { Agent, Message, Discussion } from '../types';
import { llmService } from '../services/llmService';
// 生成智能体消息
export async function generateMessage(
  agent: Agent,
  discussion: Discussion,
  topic: string,
  lastMessages: Message[] = []
): Promise<string> {
  // 智能体必须配置LLM才能生成消息
  if (!agent.llmConfig) {
    throw new Error(`智能体 ${agent.name} 未配置LLM，无法生成消息`);
  }

  try {
    return await llmService.generateAgentMessage(agent, discussion, topic, lastMessages);
  } catch (error) {
    console.error(`智能体 ${agent.name} 的LLM调用失败:`, error);
    throw new Error(`智能体 ${agent.name} 的LLM调用失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}


// 计算讨论共识度
export function calculateConsensus(messages: Message[], agents: Agent[]): number {
  if (messages.length < 3) return 0;
  
  const recentMessages = messages.slice(-10); // 分析最近10条消息
  
  // 计算各项指标
  const agreementRatio = recentMessages.filter(m => m.type === 'agreement').length / recentMessages.length;
  const disagreementRatio = recentMessages.filter(m => m.type === 'disagreement').length / recentMessages.length;
  const questionRatio = recentMessages.filter(m => m.type === 'question').length / recentMessages.length;
  
  // 参与度分析
  const participantActivity = new Map<string, number>();
  recentMessages.forEach(m => {
    participantActivity.set(m.agentId, (participantActivity.get(m.agentId) || 0) + 1);
  });
  
  const activeParticipants = participantActivity.size;
  const totalParticipants = agents.filter(a => a.isActive).length;
  const participationRatio = activeParticipants / totalParticipants;
  

  
  // 综合计算共识度
  let consensusScore = 0;
  
  // 高同意比例增加共识度
  consensusScore += agreementRatio * 40;
  
  // 低争议比例增加共识度
  consensusScore += (1 - disagreementRatio) * 30;
  
  // 适量问题表示深入讨论，但过多问题表示混乱
  consensusScore += (questionRatio > 0.1 && questionRatio < 0.3) ? 15 : 0;
  
  // 参与度
  consensusScore += participationRatio * 15;
  
  return Math.min(100, Math.max(0, consensusScore));
}
// 生成共识结论
export function generateConsensus(messages: Message[], topic: string): string {
  const recentMessages = messages.slice(-15);
  const statements = recentMessages.filter(m => m.type === 'statement' || m.type === 'agreement');
  
  if (statements.length === 0) {
    return `关于"${topic}"，参与者需要更多时间来达成共识。`;
  }
  
  // 简单的关键词提取和总结
  const keywords = extractKeywords(statements.map(m => m.content));
  
  return `经过充分讨论，大家就"${topic}"达成了共识：${keywords.slice(0, 3).join('、')}是关键要素，需要重点关注和实施。`;
}
function extractKeywords(_contents: string[]): string[] {
  const keywords = ['技术创新', '用户体验', '市场需求', '成本控制', '时间规划', '质量保证', '团队合作', '数据分析'];
  return keywords.filter(() => Math.random() > 0.6).slice(0, 5);
}
// 智能体发言间隔控制
export function getNextSpeaker(
  participants: string[],
  messages: Message[],
  mode: 'moderator' | 'free'
): string | null {
  if (participants.length === 0) return null;
  
  if (mode === 'moderator') {
    // 主持人模式：轮流发言
    const recentSpeakers = messages.slice(-participants.length).map(m => m.agentId);
    const unspokenParticipants = participants.filter(id => !recentSpeakers.includes(id));
    
    if (unspokenParticipants.length > 0) {
      return unspokenParticipants[0];
    }
    return participants[0]; // 重新开始轮流
  } else {
    // 自由讨论模式：基于活跃度和随机性
    const recentMessages = messages.slice(-10);
    const activityCount = new Map<string, number>();
    
    participants.forEach(id => activityCount.set(id, 0));
    recentMessages.forEach(m => {
      if (participants.includes(m.agentId)) {
        activityCount.set(m.agentId, (activityCount.get(m.agentId) || 0) + 1);
      }
    });
    
    // 选择活跃度较低的参与者
    const sortedByActivity = [...activityCount.entries()]
      .sort(([,a], [,b]) => a - b);
    
    const leastActive = sortedByActivity.slice(0, Math.ceil(participants.length / 2));
    const randomChoice = leastActive[Math.floor(Math.random() * leastActive.length)];
    
    return randomChoice[0];
  }
}