// LLM配置接口
export interface LLMConfig {
  id: string;
  name: string;
  provider: 'openai' | 'anthropic' | 'azure' | 'custom';
  model: string;
  apiKey: string;
  baseURL?: string;
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
}

// 智能体接口定义
export interface Agent {
  id: string;
  name: string;
  avatar: string;
  expertise: string[];        // 专业领域
  thinkingStyle: string;      // 思维方式
  personality: string;        // 性格特征
  tools: string[];           // 可用工具
  isActive: boolean;
  llmConfig: LLMConfig;      // LLM配置（必填）
}
// 消息接口定义
export interface Message {
  id: string;
  agentId: string;
  content: string;
  type: 'statement' | 'question' | 'agreement' | 'disagreement';
  timestamp: Date;
  replyTo?: string;
}
// 讨论会话接口定义
export interface Discussion {
  id: string;
  topic: string;
  mode: 'moderator' | 'free';  // 讨论模式
  participants: string[];      // 参与的智能体ID
  messages: Message[];
  status: 'active' | 'consensus' | 'ended';
  consensus: string | null;
  createdAt: Date;
  consensusScore: number;      // 共识度分数
}
// 讨论统计信息
export interface DiscussionStats {
  totalMessages: number;
  participantActivity: Record<string, number>;
  consensusHistory: number[];
  averageMessageLength: number;
  topicRelevance: number;
}
// 智能体配置选项
export interface AgentConfig {
  name: string;
  expertise: string[];
  thinkingStyle: 'logical' | 'creative' | 'analytical' | 'intuitive' | 'systematic';
  personality: 'assertive' | 'collaborative' | 'diplomatic' | 'direct' | 'thoughtful';
  tools: string[];
}
// 讨论配置
export interface DiscussionConfig {
  topic: string;
  mode: 'moderator' | 'free';
  selectedAgents: string[];
  maxMessages?: number;
  timeLimit?: number;
}

// LLM API响应接口
export interface LLMResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model?: string;
}

// LLM请求接口
export interface LLMRequest {
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  temperature?: number;
  maxTokens?: number;
  model?: string;
}

// 预设LLM配置
export interface PresetLLMConfig {
  id: string;
  name: string;
  provider: string;
  model: string;
  description: string;
  defaultSettings: {
    temperature: number;
    maxTokens: number;
  };
}